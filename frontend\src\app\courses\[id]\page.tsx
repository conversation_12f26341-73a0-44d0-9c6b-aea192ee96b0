// Server component for static params generation
export async function generateStaticParams() {
  try {
    // For static export, we'll generate a few common course IDs
    // You can expand this list or fetch from API if needed
    const commonCourseIds = ['1', '2', '3', '4', '5', '46', '47', '48', '49', '50', '56'];

    return commonCourseIds.map((id) => ({
      id: id,
    }));
  } catch (error) {
    console.warn('Error generating static params:', error);
    return [];
  }
}

'use client';

import Navbar from '../../../components/Navbar';
import QnAWithHeyGenModal from '../../../components/QnAWithHeyGenModal';
import AssessmentModal from '../../../components/AssessmentModal';
import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface Course {
  id: number;
  title: string;
  description: string;
  domain: string;
  level: string;
  estimated_hours: number;
  created_at: string;
}

interface CourseVideo {
  id: number;
  page_number: number;
  page_title: string;
  video_id: string;
  generation_status: string;
  videostatusapi: string;
  heygenvideourl: string;
  heygenbloburl: string;
  created_at: string;
  avatar_name: string;
  version_number: number;
}



export default function CourseViewPage() {
  const params = useParams();
  const router = useRouter();
  const courseId = params.id as string;

  const [activeTab, setActiveTab] = useState('Overview');
  const [showAssessmentModal, setShowAssessmentModal] = useState(false);
  const [course, setCourse] = useState<Course | null>(null);
  const [videos, setVideos] = useState<CourseVideo[]>([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [leftSidebarVisible, setLeftSidebarVisible] = useState(true);
  const [rightSidebarVisible, setRightSidebarVisible] = useState(true);
  const [hasStartedCourse, setHasStartedCourse] = useState(false);
  const [courseProgress, setCourseProgress] = useState<any>(null);
  const [moduleProgress, setModuleProgress] = useState<any[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isQnAModalOpen, setIsQnAModalOpen] = useState(false);


  // Get real user ID from authentication token
  const getUserFromToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        return JSON.parse(jsonPayload);
      } catch (error) {
        console.error('Error decoding token:', error);
        return null;
      }
    }
    return null;
  };

  // Initialize user from token
  useEffect(() => {
    const user = getUserFromToken();
    if (user) {
      setCurrentUser(user);
    } else {
      // Redirect to login if no valid token
      window.location.href = '/signin';
    }
  }, []);

  const userId = currentUser?.id;

  // Fetch user progress
  const fetchUserProgress = useCallback(async () => {
    if (!userId) return; // Don't fetch if no user ID

    try {
      const progressResponse = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/users/${userId}/courses/${courseId}/progress`);
      if (progressResponse.ok) {
        const progressData = await progressResponse.json();
        setCourseProgress(progressData.courseProgress);
        setModuleProgress(progressData.moduleProgress);

        // Set current video index based on progress
        if (progressData.courseProgress && progressData.courseProgress.current_module > 1) {
          setCurrentVideoIndex(progressData.courseProgress.current_module - 1);
        }
      }
    } catch (error) {
      console.error('Error fetching progress:', error);
    }
  }, [userId, courseId]);

  // Function to start course (track course enrollment)
  const startCourse = useCallback(async () => {
    if (!userId) return; // Don't start if no user ID

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/courses/${courseId}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (response.ok) {
        setHasStartedCourse(true);
        await fetchUserProgress(); // Refresh progress data
        console.log('Course started for course ID:', courseId);
      }
    } catch (error) {
      console.error('Error starting course:', error);
    }
  }, [userId, courseId, fetchUserProgress]);

  // Function to update module progress
  const updateModuleProgress = useCallback(async (moduleNumber: number, watchTime: number, totalDuration: number, isCompleted: boolean = false) => {
    if (!userId) return; // Don't update if no user ID

    try {
      const currentVideo = videos[moduleNumber - 1];
      const requestData = {
        userId,
        watchTime,
        totalDuration,
        isCompleted,
        moduleTitle: currentVideo?.page_title || `Module ${moduleNumber}`,
      };

      console.log('Updating module progress:', requestData);

      const response = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/courses/${courseId}/modules/${moduleNumber}/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Progress update successful:', result);
        await fetchUserProgress(); // Refresh progress data
      } else {
        const errorData = await response.json();
        console.error('Progress update failed:', errorData);
      }
    } catch (error) {
      console.error('Error updating module progress:', error);
    }
  }, [userId, courseId, videos, fetchUserProgress]);



  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        setLoading(true);

        // Fetch course details
        const courseResponse = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/courses/${courseId}`);
        if (!courseResponse.ok) {
          throw new Error('Failed to fetch course details');
        }
        const courseData = await courseResponse.json();
        setCourse(courseData);

        // Fetch course videos
        const videosResponse = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/courses/${courseId}/videos`);
        if (!videosResponse.ok) {
          throw new Error('Failed to fetch course videos');
        }
        const videosData = await videosResponse.json();
        setVideos(videosData);

        // Fetch user progress for this course (only if user is authenticated)
        if (userId) {
          await fetchUserProgress();
        }

      } catch (err: unknown) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (courseId && userId) {
      fetchCourseData();
    }
  }, [courseId, userId]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (videos.length === 0) return;

      if (event.key === 'ArrowLeft' && currentVideoIndex > 0) {
        setCurrentVideoIndex(currentVideoIndex - 1);
      } else if (event.key === 'ArrowRight' && currentVideoIndex < videos.length - 1) {
        setCurrentVideoIndex(currentVideoIndex + 1);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentVideoIndex, videos.length]);

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-[#fafbfc]">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-gray-600">Loading course...</div>
          </div>
        </main>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="flex flex-col min-h-screen bg-[#fafbfc]">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-red-600">
              {error || 'Course not found'}
            </div>
          </div>
        </main>
      </div>
    );
  }

  const currentVideo = videos[currentVideoIndex];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />
      <main className="flex-grow">
        <div className="flex h-[calc(100vh-64px)] relative">
          {/* Left Sidebar: Course Curriculum */}
          <div className={`${leftSidebarVisible ? 'w-80' : 'w-0'} bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden`}>
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
                </svg>
                <h2 className="text-lg font-semibold text-gray-800">Course Curriculum</h2>
              </div>
              {videos.length > 0 && (
                <p className="text-sm text-gray-600">{videos.length} modules</p>
              )}
            </div>

            <div className="flex-1 overflow-y-auto">
              {videos.length > 0 ? (
                <div className="p-2">
                  {videos.map((video, index) => {
                    const moduleProgressData = moduleProgress.find(mp => mp.module_number === video.page_number);
                    const isCompleted = moduleProgressData?.is_completed || false;
                    const completionPercentage = moduleProgressData?.completion_percentage || 0;

                    return (
                      <div
                        key={video.id}
                        className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ${
                          index === currentVideoIndex
                            ? 'bg-blue-50 border-l-4 border-blue-500'
                            : 'hover:bg-gray-50'
                        } ${isCompleted ? 'bg-green-50' : ''}`}
                        onClick={() => {
                          setCurrentVideoIndex(index);
                          if (!hasStartedCourse) {
                            startCourse();
                          }
                        }}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                            isCompleted
                              ? 'bg-green-500 text-white'
                              : index === currentVideoIndex
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-100 text-gray-600'
                          }`}>
                            {isCompleted ? (
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            ) : index === currentVideoIndex ? (
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              video.page_number
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className={`font-medium text-sm leading-tight ${
                              isCompleted ? 'text-green-800' :
                              index === currentVideoIndex ? 'text-blue-800' : 'text-gray-800'
                            }`}>
                              Module {video.page_number}: {video.page_title}
                            </h3>
                            <p className={`text-xs mt-1 ${
                              isCompleted ? 'text-green-600' :
                              index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'
                            }`}>
                              {video.avatar_name} • 30 min
                            </p>
                            <div className="flex items-center gap-2 mt-1">
                              <p className={`text-xs ${
                                isCompleted ? 'text-green-600' :
                                index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'
                              }`}>
                                {isCompleted ? 'Completed' : completionPercentage > 0 ? `${Math.round(completionPercentage)}% watched` : 'Not started'}
                              </p>
                              {completionPercentage > 0 && !isCompleted && (
                                <div className="flex-1 bg-gray-200 rounded-full h-1">
                                  <div
                                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                                    style={{ width: `${completionPercentage}%` }}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Take Final Assessment */}
                  <div
                    className={`mt-4 p-3 rounded-lg border cursor-pointer transition-all ${
                      courseProgress?.progress_percentage === 100
                        ? 'bg-green-50 border-green-200 hover:bg-green-100'
                        : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'
                    }`}
                    onClick={() => {
                      if (courseProgress?.progress_percentage === 100) {
                        setShowAssessmentModal(true);
                      }
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        courseProgress?.progress_percentage === 100
                          ? 'bg-green-100'
                          : 'bg-gray-100'
                      }`}>
                        <svg className={`w-4 h-4 ${
                          courseProgress?.progress_percentage === 100
                            ? 'text-green-600'
                            : 'text-gray-400'
                        }`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h3 className={`font-medium text-sm ${
                          courseProgress?.progress_percentage === 100
                            ? 'text-green-800'
                            : 'text-gray-500'
                        }`}>Take Final Assessment</h3>
                        <p className={`text-xs ${
                          courseProgress?.progress_percentage === 100
                            ? 'text-green-600'
                            : 'text-gray-400'
                        }`}>
                          {courseProgress?.progress_percentage === 100
                            ? 'Click to start assessment'
                            : 'Complete all modules to unlock assessment'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500">
                  <p>No curriculum available</p>
                </div>
              )}
            </div>
          </div>

          {/* Left Sidebar Toggle Button */}
          <button
            onClick={() => setLeftSidebarVisible(!leftSidebarVisible)}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200"
            style={{ left: leftSidebarVisible ? '320px' : '0px' }}
          >
            <svg
              className={`w-5 h-5 transition-transform duration-200 ${leftSidebarVisible ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>

          {/* Center: Video Player and Content */}
          <div className="flex-1 flex flex-col">
            {/* Video Player */}
            <div className="bg-black relative">
              {videos.length > 0 && currentVideo && currentVideo.heygenbloburl ? (
                <video
                  key={currentVideo.heygenbloburl}
                  className="w-full h-[400px] object-cover"
                  controls
                  autoPlay
                  onPlay={() => {
                    if (!hasStartedCourse) {
                      startCourse();
                    }
                  }}
                  onTimeUpdate={(e) => {
                    const video = e.target as HTMLVideoElement;
                    const watchTime = Math.floor(video.currentTime);
                    const totalDuration = Math.floor(video.duration);

                    // Update progress every 10 seconds
                    if (watchTime % 10 === 0 && watchTime > 0) {
                      updateModuleProgress(currentVideo.page_number, watchTime, totalDuration);
                    }
                  }}
                  onEnded={() => {
                    const video = document.querySelector('video') as HTMLVideoElement;
                    if (video) {
                      const totalDuration = Math.floor(video.duration);
                      updateModuleProgress(currentVideo.page_number, totalDuration, totalDuration, true);
                    }

                    // Auto-advance to next video after a short delay
                    setTimeout(() => {
                      if (currentVideoIndex < videos.length - 1) {
                        setCurrentVideoIndex(currentVideoIndex + 1);
                      }
                    }, 1500); // 1.5 second delay before auto-advancing
                  }}
                >
                  <source src={currentVideo.heygenbloburl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <div className="w-full h-[400px] flex items-center justify-center">
                  <span className="text-white text-lg">
                    {videos.length === 0 ? 'No videos available for this course' : 'Loading video...'}
                  </span>
                </div>
              )}

              {/* Hand Raise Icon Overlay */}
              <button
                onClick={() => setIsQnAModalOpen(true)}
                className="absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10"
                title="Raise Hand"
              >
                <svg
                  className="w-6 h-6 group-hover:animate-bounce"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fillRule="evenodd" d="M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z" clipRule="evenodd"/>
                </svg>
              </button>
            </div>

            {/* Tabs */}
            <div className="bg-white border-b border-gray-200">
              <div className="flex space-x-8 px-6">
                {['Overview', 'Assessment', 'Discussion', 'Reviews'].map((tab) => (
                  <button
                    key={tab}
                    className={`py-4 font-medium border-b-2 transition-colors ${
                      activeTab === tab && tab !== 'Assessment' && tab !== 'Discussion'
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => {
                      if (tab === 'Assessment') {
                        setShowAssessmentModal(true);
                      } else if (tab === 'Discussion') {
                        router.push(`/courses/${courseId}/discussion`);
                      } else {
                        setActiveTab(tab);
                      }
                    }}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1 bg-white p-6 overflow-y-auto">
              {activeTab === 'Overview' && (
                <>
                  <h2 className="text-2xl font-bold mb-4">About This Course</h2>
                  <p className="text-gray-700 mb-6">
                    Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3">What you'll learn</h3>
                      <ul className="space-y-2 text-gray-700">
                        <li className="flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          Industry best practices and standards
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          Practical implementation strategies
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          Real-world case studies and examples
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          Professional certification preparation
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3">Prerequisites</h3>
                      <p className="text-gray-700 mb-4">
                        Advanced knowledge and experience required
                      </p>

                      <h3 className="text-lg font-semibold mb-3">Course Details</h3>
                      <ul className="space-y-1 text-gray-600">
                        <li>Domain: {course.domain}</li>
                        <li>Level: {course.level}</li>
                        <li>Duration: {course.estimated_hours} hours</li>
                        <li>Modules: {videos.length}</li>
                        <li>Certificate: Available upon completion</li>
                      </ul>
                    </div>
                  </div>
                </>
              )}





              {activeTab === 'Reviews' && (
                <div className="text-gray-500 text-center py-8">Student reviews coming soon...</div>
              )}
            </div>
          </div>

          {/* Right Sidebar Toggle Button */}
          <button
            onClick={() => setRightSidebarVisible(!rightSidebarVisible)}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200"
            style={{ right: rightSidebarVisible ? '320px' : '0px' }}
          >
            <svg
              className={`w-5 h-5 transition-transform duration-200 ${rightSidebarVisible ? '' : 'rotate-180'}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>

          {/* Right Sidebar: Course Info */}
          <div className={`${rightSidebarVisible ? 'w-80' : 'w-0'} bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden`}>
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Course Info</h3>
              <h2 className="text-xl font-bold text-gray-900 mb-3">{course.title}</h2>

              <div className="mb-4">
                <span className="inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium">
                  {course.domain}
                </span>
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-1">
                  <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  {/* <span className="font-medium">4.8</span> */}
                  <span>(234 reviews)</span>
                </div>
              </div>

              <div className="space-y-2 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  <span>{course.estimated_hours} hours</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  </svg>
                  <span>{videos.length} students</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
                  </svg>
                  <span>{course.level}</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Certificate</span>
                </div>
              </div>

              {/* <button
                className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold mb-3 transition-colors"
                onClick={() => {
                  if (!hasStartedCourse) {
                    startCourse();
                  }
                }}
              >
                Start Course
              </button>

              <button className="w-full border border-gray-300 text-gray-700 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                Add to Wishlist
              </button> */}


            </div>

            {/* Current Progress */}
            {videos.length > 0 && courseProgress && (
              <div className="p-6">
                <h4 className="text-sm font-medium text-gray-500 mb-3">Course Progress</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Overall Progress</span>
                    <span className="text-sm font-semibold text-orange-600">
                      {Math.round(courseProgress.progress_percentage)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                    <div
                      className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${courseProgress.progress_percentage}%` }}
                    />
                  </div>
                  <div className="space-y-1 text-xs text-gray-600">
                    <p>{courseProgress.completed_modules} of {courseProgress.total_modules} modules completed</p>
                    <p>Status: <span className={`font-medium ${
                      courseProgress.status === 'completed' ? 'text-green-600' :
                      courseProgress.status === 'in_progress' ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {courseProgress.status === 'completed' ? 'Completed' :
                       courseProgress.status === 'in_progress' ? 'In Progress' : 'Not Started'}
                    </span></p>
                    {courseProgress.started_at && (
                      <p>Started: {new Date(courseProgress.started_at).toLocaleDateString()}</p>
                    )}
                    {courseProgress.completed_at && (
                      <p>Completed: {new Date(courseProgress.completed_at).toLocaleDateString()}</p>
                    )}
                  </div>
                </div>

                {/* Current Module Info */}
                {currentVideo && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <h5 className="text-sm font-medium text-blue-800 mb-1">Currently Watching</h5>
                    <p className="text-sm text-blue-700">
                      Module {currentVideo.page_number}: {currentVideo.page_title}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {currentVideo.avatar_name}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* QnA with HeyGen Modal */}
      <QnAWithHeyGenModal
        isOpen={isQnAModalOpen}
        onClose={() => setIsQnAModalOpen(false)}
        courseId={courseId}
      />

      {/* Assessment Modal */}
      <AssessmentModal
        isOpen={showAssessmentModal}
        onClose={() => setShowAssessmentModal(false)}
        courseId={courseId}
      />
    </div>
  );
}